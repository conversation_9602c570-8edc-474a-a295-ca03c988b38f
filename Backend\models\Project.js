import mongoose from "mongoose"

const projectSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    projectType: {
      type: String,
      enum: ["public", "private"],
      required: true,
      default: "private",
    },
    assignment: [
      {
        employee: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Employee",
          required: true,
        },
        department: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Department",
          required: true,
        },
        assignedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    priority: {
      type: String,
      enum: ["urgent", "high", "medium", "low"],
      default: "medium",
    },
    notes: {
      type: String,
      trim: true,
      maxlength: 2000,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User", // Admin who created the project
      required: true,
    },
    department: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Department", // Admin's department
      required: true,
    },
    status: {
      type: String,
      enum: ["active", "completed", "cancelled", "on-hold"],
      default: "active",
    },
    startDate: {
      type: Date,
    },
    endDate: {
      type: Date,
    },
    completedDate: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  },
)

// Index for better query performance
projectSchema.index({ createdBy: 1, status: 1 })
projectSchema.index({ department: 1, status: 1 })
projectSchema.index({ "assignment.employee": 1 })
projectSchema.index({ projectType: 1, status: 1 })
projectSchema.index({ priority: 1, status: 1 })

// Virtual for total assigned employees
projectSchema.virtual("totalAssignedEmployees").get(function () {
  return this.assignment.length
})

// Virtual for unique departments in assignment
projectSchema.virtual("assignedDepartments").get(function () {
  const departments = this.assignment.map(a => a.department.toString())
  return [...new Set(departments)]
})

// Method to add employee to project
projectSchema.methods.addEmployee = function (employeeId, departmentId) {
  // Check if employee is already assigned
  const existingAssignment = this.assignment.find(
    a => a.employee.toString() === employeeId.toString()
  )
  
  if (existingAssignment) {
    throw new Error("Employee is already assigned to this project")
  }
  
  this.assignment.push({
    employee: employeeId,
    department: departmentId,
  })
  
  return this.save()
}

// Method to remove employee from project
projectSchema.methods.removeEmployee = function (employeeId) {
  this.assignment = this.assignment.filter(
    a => a.employee.toString() !== employeeId.toString()
  )
  
  return this.save()
}

// Method to mark project as completed
projectSchema.methods.markCompleted = function () {
  this.status = "completed"
  this.completedDate = new Date()
  
  return this.save()
}

// Pre-save middleware to validate project type and assignments
projectSchema.pre("save", async function (next) {
  console.log('🔍 Pre-save middleware called for project:', this.name);
  console.log('Project type:', this.projectType);
  console.log('Is assignment modified:', this.isModified("assignment"));
  console.log('Is projectType modified:', this.isModified("projectType"));

  if (this.isModified("assignment") || this.isModified("projectType")) {
    if (this.projectType === "private") {
      // For private projects, all assigned employees must be from the same department as the creator
      const creatorDept = this.department.toString()

      console.log('🔍 Pre-save validation for private project:');
      console.log('Creator department:', creatorDept);
      console.log('Assignment departments:', this.assignment.map(a => a.department.toString()));

      for (const assignment of this.assignment) {
        const assignmentDept = assignment.department.toString()
        if (assignmentDept !== creatorDept) {
          console.log('❌ Department mismatch:', assignmentDept, '!==', creatorDept);
          return next(new Error("Private projects can only assign employees from your department"))
        }
      }

      console.log('✅ All assignments are from the same department');
    }
  }

  next()
})

// Ensure virtuals are included in JSON output
projectSchema.set("toJSON", { virtuals: true })
projectSchema.set("toObject", { virtuals: true })

export default mongoose.model("Project", projectSchema)
