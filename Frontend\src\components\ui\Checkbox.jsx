import { forwardRef } from "react"
import { cn } from "../../lib/utils"

const Checkbox = forwardRef(({ 
  className, 
  label,
  description,
  error,
  checked,
  onChange,
  disabled = false,
  ...props 
}, ref) => {
  const checkboxClasses = cn(
    "h-5 w-5 rounded border-2 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2",
    "disabled:cursor-not-allowed disabled:opacity-50",
    error 
      ? "border-red-500 dark:border-red-400 focus:ring-red-500" 
      : checked
      ? "border-blue-500 bg-blue-500 text-white focus:ring-blue-500"
      : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500 focus:ring-blue-500",
    className
  )
  
  return (
    <div className="space-y-2">
      <div className="flex items-start space-x-3">
        <div className="relative flex items-center">
          <input
            ref={ref}
            type="checkbox"
            className={checkboxClasses}
            checked={checked}
            onChange={onChange}
            disabled={disabled}
            {...props}
          />
          {checked && (
            <svg 
              className="absolute inset-0 w-5 h-5 text-white pointer-events-none" 
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path 
                fillRule="evenodd" 
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                clipRule="evenodd" 
              />
            </svg>
          )}
        </div>
        
        {label && (
          <div className="flex-1">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">
              {label}
            </label>
            {description && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {description}
              </p>
            )}
          </div>
        )}
      </div>
      
      {error && (
        <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </p>
      )}
    </div>
  )
})

Checkbox.displayName = "Checkbox"

export { Checkbox }
