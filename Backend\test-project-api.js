import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api';

async function testProjectAPI() {
  console.log('🚀 Testing Project Management API...\n');

  try {
    // Step 1: Login as Admin
    console.log('1️⃣ Logging in as Admin (testadmin_project)...');
    const adminLogin = await axios.post(`${BASE_URL}/auth/admin/login`, {
      username: 'testadmin_project',
      password: 'TestAdmin123!'
    });
    const adminToken = adminLogin.data.token;
    const admin = adminLogin.data.user;
    console.log(`✅ Admin logged in: ${admin.name} (Department: ${admin.department.name})\n`);

    // Step 2: Get departments for public project testing
    console.log('2️⃣ Getting available departments...');
    const departmentsResponse = await axios.get(`${BASE_URL}/department/all`);
    const departments = departmentsResponse.data.departments;
    console.log(`✅ Found ${departments.length} departments:`);
    departments.forEach(dept => console.log(`   - ${dept.name}`));
    console.log('');

    // Step 3: Get employees from admin's department for private project
    console.log('3️⃣ Getting employees from admin\'s department...');
    const employeesResponse = await axios.get(`${BASE_URL}/project/employees/department/${admin.department._id}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const departmentEmployees = employeesResponse.data.employees;
    console.log(`✅ Found ${departmentEmployees.length} employees in ${admin.department.name}:`);
    departmentEmployees.forEach(emp => console.log(`   - ${emp.name} (${emp.email})`));
    console.log('');

    // Step 4: Create a private project
    console.log('4️⃣ Creating a private project...');
    const privateProjectData = {
      name: 'HR Internal Process Improvement',
      description: 'Streamline internal HR processes and documentation',
      projectType: 'private',
      priority: 'high',
      notes: 'This is a private project for HR department only',
      assignment: departmentEmployees.slice(0, 2).map(emp => ({
        employee: emp._id,
        department: admin.department._id
      }))
    };

    const privateProjectResponse = await axios.post(`${BASE_URL}/project/create`, privateProjectData, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const privateProject = privateProjectResponse.data.project;
    console.log(`✅ Private project created: ${privateProject.name}`);
    console.log(`   - Type: ${privateProject.projectType}`);
    console.log(`   - Priority: ${privateProject.priority}`);
    console.log(`   - Assigned employees: ${privateProject.assignment.length}`);
    console.log('');

    // Step 5: Get employees from another department for public project
    const otherDepartment = departments.find(d => d._id !== admin.department._id);
    if (otherDepartment) {
      console.log('5️⃣ Getting employees from another department for public project...');
      const otherDeptEmployeesResponse = await axios.get(`${BASE_URL}/project/employees/department/${otherDepartment._id}`, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      const otherDeptEmployees = otherDeptEmployeesResponse.data.employees;
      console.log(`✅ Found ${otherDeptEmployees.length} employees in ${otherDepartment.name}`);

      // Step 6: Create a public project with cross-department assignment
      console.log('6️⃣ Creating a public project with cross-department assignment...');
      const publicProjectData = {
        name: 'Company-wide Digital Transformation',
        description: 'Implement new digital tools across all departments',
        projectType: 'public',
        priority: 'urgent',
        notes: 'This project involves multiple departments working together',
        assignment: [
          // Assign from admin's department
          ...departmentEmployees.slice(0, 1).map(emp => ({
            employee: emp._id,
            department: admin.department._id
          })),
          // Assign from other department
          ...otherDeptEmployees.slice(0, 1).map(emp => ({
            employee: emp._id,
            department: otherDepartment._id
          }))
        ]
      };

      const publicProjectResponse = await axios.post(`${BASE_URL}/project/create`, publicProjectData, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      const publicProject = publicProjectResponse.data.project;
      console.log(`✅ Public project created: ${publicProject.name}`);
      console.log(`   - Type: ${publicProject.projectType}`);
      console.log(`   - Priority: ${publicProject.priority}`);
      console.log(`   - Assigned employees: ${publicProject.assignment.length}`);
      console.log(`   - Departments involved: ${publicProject.assignedDepartments.length}`);
      console.log('');
    }

    // Step 7: Get all projects for the admin
    console.log('7️⃣ Getting all projects for admin...');
    const projectsResponse = await axios.get(`${BASE_URL}/project/all`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const projects = projectsResponse.data.projects;
    console.log(`✅ Found ${projects.length} projects accessible to admin:`);
    projects.forEach(project => {
      console.log(`   - ${project.name} (${project.projectType}, ${project.priority})`);
      console.log(`     Assigned: ${project.assignment.length} employees`);
    });
    console.log('');

    // Step 8: Update a project
    if (projects.length > 0) {
      console.log('8️⃣ Testing project update...');
      const projectToUpdate = projects[0];
      const updateData = {
        priority: 'medium',
        notes: 'Updated notes: Project priority changed to medium'
      };

      const updateResponse = await axios.put(`${BASE_URL}/project/${projectToUpdate._id}`, updateData, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      console.log(`✅ Project updated: ${updateResponse.data.project.name}`);
      console.log(`   - New priority: ${updateResponse.data.project.priority}`);
      console.log('');
    }

    console.log('🎉 Project Management API Test Completed Successfully!\n');
    
    console.log('📋 Test Summary:');
    console.log('✅ Admin authentication');
    console.log('✅ Department and employee retrieval');
    console.log('✅ Private project creation');
    console.log('✅ Public project creation with cross-department assignment');
    console.log('✅ Project listing and filtering');
    console.log('✅ Project updates');
    console.log('');
    
    console.log('🌐 Frontend Testing:');
    console.log(`• Admin Login: http://localhost:5174/admin/login`);
    console.log(`• Use credentials: testadmin_project / TestAdmin123!`);
    console.log(`• Test project management from the admin dashboard`);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testProjectAPI();
