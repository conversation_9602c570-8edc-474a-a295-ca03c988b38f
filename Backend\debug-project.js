import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api';

async function debugProject() {
  try {
    // Login as Admin
    const adminLogin = await axios.post(`${BASE_URL}/auth/admin/login`, {
      username: 'testadmin_project',
      password: 'TestAdmin123!'
    });
    const adminToken = adminLogin.data.token;
    const admin = adminLogin.data.user;
    
    console.log('Admin data:', JSON.stringify(admin, null, 2));
    console.log('Admin department ID:', admin.department._id);
    console.log('Admin department ID type:', typeof admin.department._id);

    // Get employees from admin's department
    const employeesResponse = await axios.get(`${BASE_URL}/project/employees/department/${admin.department._id}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const departmentEmployees = employeesResponse.data.employees;
    
    console.log('First employee:', departmentEmployees[0]);
    console.log('Employee department from API:', departmentEmployees[0]?.department);

    // Try creating a simple private project with one employee
    const privateProjectData = {
      name: 'Debug Private Project',
      description: 'Testing private project creation',
      projectType: 'private',
      priority: 'medium',
      assignment: [{
        employee: departmentEmployees[0]._id,
        department: admin.department._id
      }]
    };

    console.log('Project data being sent:', JSON.stringify(privateProjectData, null, 2));

    const privateProjectResponse = await axios.post(`${BASE_URL}/project/create`, privateProjectData, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    console.log('✅ Project created successfully!');
    console.log(privateProjectResponse.data);

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Full response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

debugProject();
