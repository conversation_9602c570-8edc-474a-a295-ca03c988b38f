import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api';

async function testProjectUpdate() {
  console.log('🚀 Testing Project Update Functionality...\n');

  try {
    // Step 1: Login as Admin
    console.log('1️⃣ Logging in as Admin...');
    const adminLogin = await axios.post(`${BASE_URL}/auth/admin/login`, {
      username: 'testadmin_project',
      password: 'TestAdmin123!'
    });
    const adminToken = adminLogin.data.token;
    const admin = adminLogin.data.user;
    console.log(`✅ Admin logged in: ${admin.name} (Department: ${admin.department.name})\n`);

    // Step 2: Get departments
    console.log('2️⃣ Getting departments...');
    const departmentsResponse = await axios.get(`${BASE_URL}/department/all`);
    const departments = departmentsResponse.data.departments;
    console.log(`✅ Found ${departments.length} departments\n`);

    // Step 3: Get employees from HR department
    console.log('3️⃣ Getting HR employees...');
    const hrEmployeesResponse = await axios.get(`${BASE_URL}/project/employees/department/${admin.department._id}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const hrEmployees = hrEmployeesResponse.data.employees;
    console.log(`✅ Found ${hrEmployees.length} HR employees\n`);

    // Step 4: Create a public project
    console.log('4️⃣ Creating a public project...');
    const projectData = {
      name: 'Test Update Project',
      description: 'Project for testing update functionality',
      projectType: 'public',
      priority: 'high',
      assignment: [
        {
          employee: hrEmployees[0]._id,
          department: admin.department._id
        }
      ]
    };

    const createResponse = await axios.post(`${BASE_URL}/project/create`, projectData, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const project = createResponse.data.project;
    console.log(`✅ Project created: ${project.name} (ID: ${project._id})\n`);

    // Step 5: Update the project
    console.log('5️⃣ Updating the project...');
    const updateData = {
      name: 'Updated Test Project',
      description: 'Updated description',
      priority: 'medium',
      assignment: [
        // Keep existing employee
        {
          employee: hrEmployees[0]._id,
          department: admin.department._id
        },
        // Add another employee if available
        ...(hrEmployees.length > 1 ? [{
          employee: hrEmployees[1]._id,
          department: admin.department._id
        }] : [])
      ]
    };

    const updateResponse = await axios.put(`${BASE_URL}/project/${project._id}`, updateData, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const updatedProject = updateResponse.data.project;
    console.log(`✅ Project updated: ${updatedProject.name}`);
    console.log(`   - Priority: ${updatedProject.priority}`);
    console.log(`   - Assigned employees: ${updatedProject.assignment.length}\n`);

    // Step 6: Get the updated project
    console.log('6️⃣ Retrieving updated project...');
    const getResponse = await axios.get(`${BASE_URL}/project/${project._id}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const retrievedProject = getResponse.data.project;
    console.log(`✅ Retrieved project: ${retrievedProject.name}`);
    console.log(`   - Assignments: ${retrievedProject.assignment.length} employees`);
    retrievedProject.assignment.forEach((assignment, index) => {
      console.log(`     ${index + 1}. ${assignment.employee.name} (${assignment.department.name})`);
    });

    console.log('\n🎉 Project Update Test Completed Successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testProjectUpdate();
