import jwt from "jsonwebtoken"
import User from "../models/User.js"
import Employee from "../models/Employee.js"

export const verifyToken = async (req, res, next) => {
  try {
    console.log('🔐 verifyToken middleware called for:', req.method, req.path);
    const token = req.header("Authorization")?.replace("Bearer ", "")

    if (!token) {
      console.log('❌ No token provided');
      return res.status(401).json({
        success: false,
        message: "Access denied. No token provided.",
      })
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET)

    let user
    if (decoded.role === "employee") {
      user = await Employee.findById(decoded.id).populate("department")
    } else {
      user = await User.findById(decoded.id).populate("department")
    }

    if (!user || !user.isActive) {
      return res.status(401).json({
        success: false,
        message: "Invalid token or user inactive",
      })
    }

    req.user = {
      id: user._id.toString(),
      role: decoded.role,
      department: user.department,
      permissions: user.permissions || null, // Add permissions for superadmins
    }

    next()
  } catch (error) {
    res.status(401).json({
      success: false,
      message: "Invalid token",
    })
  }
}

export const superAdminOnly = (req, res, next) => {
  if (req.user.role !== "superadmin") {
    return res.status(403).json({
      success: false,
      message: "Access denied. Super admin only.",
    })
  }
  next()
}

export const adminOrSuperAdmin = (req, res, next) => {
  console.log('DEBUG: adminOrSuperAdmin middleware - user role:', req.user.role);
  if (req.user.role !== "admin" && req.user.role !== "superadmin") {
    return res.status(403).json({
      success: false,
      message: "Access denied. Admin privileges required.",
    })
  }
  next()
}

export const employeeOnly = (req, res, next) => {
  if (req.user.role !== "employee") {
    return res.status(403).json({
      success: false,
      message: "Access denied. Employee only.",
    })
  }
  next()
}

// Middleware for superadmin with read-write permissions only
export const superAdminReadWrite = (req, res, next) => {
  if (req.user.role !== "superadmin" || req.user.permissions !== "read-write") {
    return res.status(403).json({
      success: false,
      message: "Access denied. Super Admin with read-write permissions required.",
    })
  }
  next()
}

// Middleware for any superadmin (read-write or read-only)
export const anySuperAdmin = (req, res, next) => {
  if (req.user.role !== "superadmin") {
    return res.status(403).json({
      success: false,
      message: "Access denied. Super Admin role required.",
    })
  }
  next()
}
