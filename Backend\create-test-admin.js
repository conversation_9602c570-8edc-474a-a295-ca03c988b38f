import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from './models/User.js';
import Department from './models/Department.js';

dotenv.config();

async function createTestAdmin() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    
    // Get HR department
    const hrDepartment = await Department.findOne({ name: 'HR' });
    if (!hrDepartment) {
      console.log('❌ HR department not found');
      process.exit(1);
    }

    // Check if test admin already exists
    const existingAdmin = await User.findOne({ username: 'testadmin_project' });
    if (existingAdmin) {
      console.log('✅ Test admin already exists: testadmin_project / TestAdmin123!');
      console.log(`   Department: ${hrDepartment.name}`);
      process.exit(0);
    }

    // Create test admin
    const testAdmin = new User({
      name: 'Project Test Admin',
      email: '<EMAIL>',
      username: 'testadmin_project',
      password: 'TestAdmin123!',
      role: 'admin',
      department: hrDepartment._id
    });

    await testAdmin.save();
    console.log('✅ Created test admin for project testing:');
    console.log('   Username: testadmin_project');
    console.log('   Password: TestAdmin123!');
    console.log(`   Department: ${hrDepartment.name}`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

createTestAdmin();
