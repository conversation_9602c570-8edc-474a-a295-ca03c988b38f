import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api';

async function debugPrivateProject() {
  try {
    // Login as Admin
    console.log('1️⃣ Logging in as Admin...');
    const adminLogin = await axios.post(`${BASE_URL}/auth/admin/login`, {
      username: 'testadmin_project',
      password: 'TestAdmin123!'
    });
    const adminToken = adminLogin.data.token;
    const admin = adminLogin.data.user;
    
    console.log('Admin data:', JSON.stringify(admin, null, 2));

    // Get employees from admin's department
    console.log('2️⃣ Getting employees from admin department...');
    const employeesResponse = await axios.get(`${BASE_URL}/project/employees/department/${admin.department._id}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    const employees = employeesResponse.data.employees;
    
    console.log('Employees:', employees.map(emp => ({ id: emp._id, name: emp.name, department: emp.department })));

    // Try creating a simple private project
    console.log('3️⃣ Creating private project...');
    const projectData = {
      name: 'Debug Private Project',
      description: 'Testing private project creation',
      projectType: 'private',
      priority: 'medium',
      assignment: [{
        employee: employees[0]._id,
        department: admin.department._id
      }]
    };

    console.log('Project data being sent:', JSON.stringify(projectData, null, 2));

    const response = await axios.post(`${BASE_URL}/project/create`, projectData, {
      headers: { 
        Authorization: `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Project created successfully!');
    console.log(response.data);

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    if (error.response?.status) {
      console.error('Status:', error.response.status);
    }
    if (error.response?.data) {
      console.error('Full response:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

debugPrivateProject();
