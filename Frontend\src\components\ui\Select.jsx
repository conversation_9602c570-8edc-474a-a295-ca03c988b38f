import { forwardRef, useState } from "react"
import { cn } from "../../lib/utils"

const Select = forwardRef(({ 
  className, 
  label,
  error,
  helperText,
  options = [],
  placeholder = "Select an option",
  value,
  onChange,
  disabled = false,
  ...props 
}, ref) => {
  const [focused, setFocused] = useState(false)
  
  const selectClasses = cn(
    "flex h-12 w-full rounded-lg border-2 bg-white dark:bg-gray-800 px-3 py-2 text-sm transition-all duration-300",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",
    "disabled:cursor-not-allowed disabled:opacity-50",
    error 
      ? "border-red-500 dark:border-red-400 focus:border-red-500 focus:ring-red-500" 
      : focused
      ? "border-blue-500 dark:border-blue-400 focus:border-blue-500 focus:ring-blue-500"
      : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500",
    className
  )
  
  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
      )}
      
      <select
        ref={ref}
        className={selectClasses}
        value={value || ""}
        onChange={onChange}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        disabled={disabled}
        {...props}
      >
        <option value="" disabled>
          {placeholder}
        </option>
        {options.map((option) => (
          <option 
            key={option.value} 
            value={option.value}
            className="text-gray-900 dark:text-white bg-white dark:bg-gray-800"
          >
            {option.label}
          </option>
        ))}
      </select>
      
      {error && (
        <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {helperText}
        </p>
      )}
    </div>
  )
})

Select.displayName = "Select"

export { Select }
