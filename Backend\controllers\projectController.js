import Project from "../models/Project.js"
import Employee from "../models/Employee.js"
import Department from "../models/Department.js"

// Create a new project (Admin only)
export const createProject = async (req, res) => {
  try {
    console.log('DEBUG: createProject called');
    console.log('DEBUG: req.user:', req.user);
    console.log('DEBUG: req.body:', req.body);

    const {
      name,
      description,
      projectType,
      assignment,
      priority,
      notes,
      startDate,
      endDate,
    } = req.body

    // Validate required fields
    if (!name || !projectType) {
      return res.status(400).json({
        success: false,
        message: "Project name and type are required",
      })
    }

    // Validate project type
    if (!["public", "private"].includes(projectType)) {
      return res.status(400).json({
        success: false,
        message: "Project type must be either 'public' or 'private'",
      })
    }

    // Validate priority
    if (priority && !["urgent", "high", "medium", "low"].includes(priority)) {
      return res.status(400).json({
        success: false,
        message: "Priority must be one of: urgent, high, medium, low",
      })
    }

    // Create project
    const project = new Project({
      name,
      description,
      projectType,
      priority: priority || "medium",
      notes,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      createdBy: req.user.id,
      department: req.user.department,
      assignment: [],
    })

    // Process employee assignments if provided
    if (assignment && Array.isArray(assignment) && assignment.length > 0) {
      for (const emp of assignment) {
        if (!emp.employee || !emp.department) {
          return res.status(400).json({
            success: false,
            message: "Each assignment must have employee and department",
          })
        }

        // Verify employee exists and belongs to the specified department
        const employee = await Employee.findById(emp.employee)
        console.log('DEBUG: Found employee:', employee ? employee.name : 'null');
        console.log('DEBUG: Employee department:', employee ? employee.department.toString() : 'null');
        console.log('DEBUG: Assignment department:', emp.department.toString());
        if (!employee) {
          return res.status(400).json({
            success: false,
            message: `Employee with ID ${emp.employee} not found`,
          })
        }

        if (employee.department.toString() !== emp.department.toString()) {
          return res.status(400).json({
            success: false,
            message: `Employee ${employee.name} does not belong to the specified department`,
          })
        }

        // For private projects, ensure employee is from admin's department
        if (projectType === "private" && employee.department.toString() !== req.user.department.toString()) {
          console.log('DEBUG: Private project validation failed');
          console.log('Employee department:', employee.department.toString());
          console.log('Admin department:', req.user.department.toString());
          console.log('Employee:', employee.name);
          return res.status(400).json({
            success: false,
            message: "Private projects can only assign employees from your department",
          })
        }

        project.assignment.push({
          employee: emp.employee,
          department: emp.department,
        })
      }
    }

    await project.save()

    // Populate the project with employee and department details
    await project.populate([
      {
        path: "assignment.employee",
        select: "name email username",
      },
      {
        path: "assignment.department",
        select: "name",
      },
      {
        path: "createdBy",
        select: "name email",
      },
      {
        path: "department",
        select: "name",
      },
    ])

    res.status(201).json({
      success: true,
      message: "Project created successfully",
      project,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get all projects for admin (filtered by department for admins)
export const getAllProjects = async (req, res) => {
  try {
    const { status, priority, projectType, page = 1, limit = 10 } = req.query

    // Build filter based on user role
    let filter = { isActive: true }

    // Admins can only see projects they created or projects from their department
    if (req.user.role === "admin") {
      filter.$or = [
        { createdBy: req.user.id },
        { department: req.user.department },
      ]
    }

    // Apply additional filters
    if (status) filter.status = status
    if (priority) filter.priority = priority
    if (projectType) filter.projectType = projectType

    const skip = (parseInt(page) - 1) * parseInt(limit)

    const projects = await Project.find(filter)
      .populate([
        {
          path: "assignment.employee",
          select: "name email username",
        },
        {
          path: "assignment.department",
          select: "name",
        },
        {
          path: "createdBy",
          select: "name email",
        },
        {
          path: "department",
          select: "name",
        },
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))

    const total = await Project.countDocuments(filter)

    res.json({
      success: true,
      projects,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get project by ID
export const getProjectById = async (req, res) => {
  try {
    const { projectId } = req.params

    const project = await Project.findById(projectId)
      .populate([
        {
          path: "assignment.employee",
          select: "name email username contactNumber",
        },
        {
          path: "assignment.department",
          select: "name description",
        },
        {
          path: "createdBy",
          select: "name email",
        },
        {
          path: "department",
          select: "name description",
        },
      ])

    if (!project) {
      return res.status(404).json({
        success: false,
        message: "Project not found",
      })
    }

    // Check if admin has access to this project
    if (req.user.role === "admin") {
      const hasAccess = 
        project.createdBy._id.toString() === req.user.id ||
        project.department._id.toString() === req.user.department.toString()

      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: "Access denied",
        })
      }
    }

    res.json({
      success: true,
      project,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Update project
export const updateProject = async (req, res) => {
  try {
    const { projectId } = req.params
    const updates = req.body

    const project = await Project.findById(projectId)

    if (!project) {
      return res.status(404).json({
        success: false,
        message: "Project not found",
      })
    }

    // Check if admin has permission to update this project
    if (req.user.role === "admin" && project.createdBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only update projects you created",
      })
    }

    // Validate updates
    if (updates.projectType && !["public", "private"].includes(updates.projectType)) {
      return res.status(400).json({
        success: false,
        message: "Project type must be either 'public' or 'private'",
      })
    }

    if (updates.priority && !["urgent", "high", "medium", "low"].includes(updates.priority)) {
      return res.status(400).json({
        success: false,
        message: "Priority must be one of: urgent, high, medium, low",
      })
    }

    if (updates.status && !["active", "completed", "cancelled", "on-hold"].includes(updates.status)) {
      return res.status(400).json({
        success: false,
        message: "Status must be one of: active, completed, cancelled, on-hold",
      })
    }

    // Handle assignment updates
    if (updates.assignment) {
      // Validate and process new assignments
      const newAssignments = []
      
      for (const emp of updates.assignment) {
        if (!emp.employee || !emp.department) {
          return res.status(400).json({
            success: false,
            message: "Each assignment must have employee and department",
          })
        }

        // Verify employee exists and belongs to the specified department
        const employee = await Employee.findById(emp.employee)
        if (!employee) {
          return res.status(400).json({
            success: false,
            message: `Employee with ID ${emp.employee} not found`,
          })
        }

        if (employee.department.toString() !== emp.department.toString()) {
          return res.status(400).json({
            success: false,
            message: `Employee ${employee.name} does not belong to the specified department`,
          })
        }

        // For private projects, ensure employee is from admin's department
        const projectType = updates.projectType || project.projectType
        if (projectType === "private" && employee.department.toString() !== req.user.department.toString()) {
          return res.status(400).json({
            success: false,
            message: "Private projects can only assign employees from your department",
          })
        }

        newAssignments.push({
          employee: emp.employee,
          department: emp.department,
        })
      }

      updates.assignment = newAssignments
    }

    // Update completion date if status is being set to completed
    if (updates.status === "completed" && project.status !== "completed") {
      updates.completedDate = new Date()
    }

    // Apply updates
    Object.assign(project, updates)
    await project.save()

    // Populate the updated project
    await project.populate([
      {
        path: "assignment.employee",
        select: "name email username",
      },
      {
        path: "assignment.department",
        select: "name",
      },
      {
        path: "createdBy",
        select: "name email",
      },
      {
        path: "department",
        select: "name",
      },
    ])

    res.json({
      success: true,
      message: "Project updated successfully",
      project,
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Delete project (soft delete)
export const deleteProject = async (req, res) => {
  try {
    const { projectId } = req.params

    const project = await Project.findById(projectId)

    if (!project) {
      return res.status(404).json({
        success: false,
        message: "Project not found",
      })
    }

    // Check if admin has permission to delete this project
    if (req.user.role === "admin" && project.createdBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only delete projects you created",
      })
    }

    // Soft delete
    project.isActive = false
    await project.save()

    res.json({
      success: true,
      message: "Project deleted successfully",
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get employees by department (for assignment)
export const getEmployeesByDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params

    // Verify department exists
    const department = await Department.findById(departmentId)
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Department not found",
      })
    }

    // Get active employees from the department
    const employees = await Employee.find({
      department: departmentId,
      isActive: true,
      isEmailVerified: true,
    }).select("name email username contactNumber")

    res.json({
      success: true,
      employees,
      department: {
        id: department._id,
        name: department.name,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}
