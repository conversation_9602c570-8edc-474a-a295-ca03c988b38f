import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from './models/User.js';
import Department from './models/Department.js';

dotenv.config();

async function checkAdmins() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    
    const admins = await User.find({ role: 'admin' }).populate('department');
    const departments = await Department.find();
    
    console.log('=== DEPARTMENTS ===');
    departments.forEach(dept => {
      console.log(`- ${dept.name} (ID: ${dept._id})`);
    });
    
    console.log('\n=== ADMINS ===');
    if (admins.length === 0) {
      console.log('No admins found. Creating test admin...');
      
      const testAdmin = new User({
        name: 'Test Admin',
        email: '<EMAIL>',
        username: 'testadmin',
        password: 'TestAdmin123!',
        role: 'admin',
        department: departments[0]._id
      });
      
      await testAdmin.save();
      console.log(`✅ Created test admin: testadmin / TestAdmin123! (Department: ${departments[0].name})`);
    } else {
      admins.forEach(admin => {
        console.log(`- ${admin.name} (${admin.username}) - Department: ${admin.department?.name || 'None'}`);
      });
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkAdmins();
