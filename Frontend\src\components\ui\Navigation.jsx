import { useState } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useAuth } from "../../contexts/AuthContext"
import { Button } from "./Button"
import { ThemeToggle } from "./ThemeToggle"
import { Avatar } from "./Avatar"
import { Badge } from "./Badge"
import { cn } from "../../lib/utils"

export function Navigation({ title, subtitle, actions = [] }) {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  
  const getRoleColor = (role) => {
    switch (role) {
      case "superadmin":
        return "primary"
      case "admin":
        return "warning"
      case "employee":
        return "success"
      default:
        return "default"
    }
  }
  
  const getHomeRoute = () => {
    switch (user?.role) {
      case "superadmin":
        return "/superadmin/dashboard"
      case "admin":
        return "/admin/dashboard"
      case "employee":
        return "/employee/dashboard"
      default:
        return "/"
    }
  }
  
  return (
    <nav className="sticky top-0 z-50 bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Logo and title */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate(getHomeRoute())}
              className="group flex items-center space-x-3 hover:scale-105 transition-all duration-300"
            >
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                  {title || "TaskManager"}
                </h1>
                {subtitle && (
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    {subtitle}
                  </p>
                )}
              </div>
            </button>
          </div>
          
          {/* Center - Actions */}
          <div className="hidden md:flex items-center space-x-2">
            {actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || "ghost"}
                size="sm"
                onClick={action.onClick}
                className={action.className}
              >
                {action.icon && <span className="mr-2">{action.icon}</span>}
                {action.label}
              </Button>
            ))}
          </div>
          
          {/* Right side - User menu */}
          <div className="flex items-center space-x-4">
            {/* User info */}
            {user && (
              <div className="hidden sm:flex items-center space-x-4 bg-slate-50/50 dark:bg-slate-800/50 rounded-xl px-4 py-2 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50">
                <div className="text-right">
                  <p className="text-sm font-semibold text-slate-900 dark:text-white">
                    {user.name}
                  </p>
                  <div className="flex items-center space-x-2">
                    <Badge variant={getRoleColor(user.role)} size="sm">
                      {user.role}
                    </Badge>
                    {user.department && (
                      <span className="text-xs text-slate-500 dark:text-slate-400 font-medium">
                        {user.department.name}
                      </span>
                    )}
                  </div>
                </div>
                <Avatar
                  src={user.avatar}
                  alt={user.name}
                  size="default"
                  status="online"
                  className="ring-2 ring-indigo-500/20"
                />
              </div>
            )}
            
            {/* Theme toggle */}
            <ThemeToggle />
            
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
                />
              </svg>
            </button>
            
            {/* Logout button */}
            {user && (
              <Button
                variant="glass"
                size="sm"
                onClick={logout}
                className="hidden sm:flex backdrop-blur-sm hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  />
                </svg>
                Logout
              </Button>
            )}
          </div>
        </div>
        
        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200 dark:border-gray-700">
            <div className="space-y-2">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || "ghost"}
                  size="sm"
                  onClick={() => {
                    action.onClick()
                    setIsMenuOpen(false)
                  }}
                  className={cn("w-full justify-start", action.className)}
                >
                  {action.icon && <span className="mr-2">{action.icon}</span>}
                  {action.label}
                </Button>
              ))}
              
              {user && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    logout()
                    setIsMenuOpen(false)
                  }}
                  className="w-full justify-start"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                  </svg>
                  Logout
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
